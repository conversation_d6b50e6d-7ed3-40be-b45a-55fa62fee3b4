<configuration debug="false">
<!--    <logger name="org.apache" level="DEBUG" />-->
<!--    <logger name="org.apache" level="INFO" />-->
    <logger name="org.apache" level="ERROR" />
    <logger name="org.apache.http.wire" level="ERROR" />
    <logger name="org.apache.http.headers" level="ERROR" />

    <property name="CONSOLE_LOG_PATTERN"
              value="%date{yyyy-MM-dd HH:mm:ss}  %highlight(%-5level) %magenta(%-4relative) --- [%yellow(%15.15thread)] %cyan(%-40.40logger{39}) : %msg%n"/>


    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
        </encoder>
    </appender>

    <root level="INFO">
        <appender-ref ref="STDOUT"/>
    </root>
</configuration>