
## 由于微信二维码导致博客园总目录被封，如需加 尼恩 提取资源，还请扫码下面的二维码，发送 “领电子书”即可


![在这里插入图片描述](https://img-blog.csdnimg.cn/76fa1d09f9f74819830c9df54fcaf11f.png#pic_center)

**疯狂创客圈： 一个Java 超高并发、超高性能 发烧友圈子（圈内有大厂BOY、技术高手、Java大神 ）卷王圈子, 欢迎大家一起来卷**




------

## 尼恩独家2：价值 > 10000元 尼恩Java 面试宝典

> 35个Java面试专题 : 含最新 Java 大厂面试题、Java 高级开发面试题 好几千道。并且持续 更新。

[**查看 尼恩Java 面试宝典 最新的版本升级说明，请点击这里**](https://www.cnblogs.com/crazymakercircle/p/13917138.html)

![在这里插入图片描述](https://img-blog.csdnimg.cn/84429fbe84d243179c17242de9ab422c.png)



注意：这个是独家资源，不断升级，只有尼恩本人，才有**最新版，最新版，最新版**

> 领取方式：加尼恩微信，发送 “领电子书”  四字， 免费领取。

## 尼恩独家2：大厂必备、面试必备的 价值 >500元 的 尼恩编著 Java高并发三部曲（ 作者亲手送书 ）

免费赠送尼恩编著的 大厂必备、架构必备、高薪必备 电子版图书 （亲手免费送书，市场价 > 300元)

- **第1卷：《Java高并发核心编程（卷1）加强版：NIO、Netty、Redis、ZooKeeper》 [详情](https://www.cnblogs.com/crazymakercircle/p/14493539.html)**
- **第2卷：《Java高并发核心编程（卷2）加强版：多线程、锁、JMM、JUC、高并发设计模式》 [详情](https://www.cnblogs.com/crazymakercircle/p/14493539.html)**
- **第3卷： 《Java高并发核心编程（卷3）加强版: 亿级用户WEB架构与实操》 [详情](https://www.cnblogs.com/crazymakercircle/p/14493539.html)**
- **老版本： 《Netty Zookeeper Redis高并发核心编程》 《Spring Cloud Nginx高并发核心编程》**


![在这里插入图片描述](https://img-blog.csdnimg.cn/a317c8ab6abd4dd7ae8b9133aed01cfa.png)
老版本
![img.png](https://img-blog.csdnimg.cn/47835fb8ed9247d7bde937250cbff239.jpeg)
最新版本 （电子版，不断升级中......）
![在这里插入图片描述](https://img-blog.csdnimg.cn/951417c3b35b40c7adf531cff4029016.jpeg)

注意：这个是独家资源，不断升级，

只有尼恩本人，才有**最新版，最新版，最新版**

> 领取方式：加尼恩微信，发送 “领电子书”  四字， 免费领取。



## 尼恩20年来的独家私藏：3高架构行业案例

![在这里插入图片描述](https://img-blog.csdnimg.cn/8dda5074428349b29b5e2c5e5bf80990.png)



## 尼恩独家：价值连城，200多篇硬核的生产项目实操笔记

200多篇硬核的生产项目实操笔记，更多的实操笔记，后面都会以pdf 版本发布， 老架构师的独家私货，非常宝贵，一般都都不会拿出来。

![在这里插入图片描述](https://img-blog.csdnimg.cn/453c48d8fefa43039b6e84e80e867c41.png)



# **技术难题社群交流：社群不少一线大厂小伙伴、架构师、技术高手，可进行 技术问题、解决方案 取经** [![img](https://img-blog.csdnimg.cn/20210306215529555.png)](https://img-blog.csdnimg.cn/20210306215529555.png)

[![img](https://img-blog.csdnimg.cn/20210627092632136.png)](https://img-blog.csdnimg.cn/20210627092632136.png)

------

## 社群超高并发实操(高薪必备)：千万级 IM 消息中台 真刀实操 （已经完成）

[Java 高并发社群——疯狂创客圈的 重点项目， 学习完毕之后，能彻底掌握Netty](https://www.cnblogs.com/crazymakercircle/p/9904544.html)

**【CrazyIM ：分布式 聊天室】 亿级流量IM 社群开源项目**

链接：[实战Netty集群](https://www.cnblogs.com/crazymakercircle/p/11470287.html)    亿级流量IM 社群开源项目 crazyim 源码： https://gitee.com/crazymaker/crazy_tourist_circle__im.git

对应到的是 **《[Netty  Redis Zookeeper 高并发实战](https://www.cnblogs.com/crazymakercircle/p/11397271.html)》一书，第12章的源码** **请参考  《[Netty  Redis Zookeeper 高并发实战](https://www.cnblogs.com/crazymakercircle/p/11397271.html)》一书**

基于**CrazyIM的生产项目：橙交（带语音聊天、视频聊天功能）**

**【Netty灵魂实验】 在本地 完成 100W连接 高并发实验，瞬间内力大增，实力爆表**


------

## 史上最全的 Java 面试题 30 专题

| **精心梳理、吐血推荐、史上最强、建议收藏**                   | **阿里、京东、美团、头条.... \*随意挑、横着走！！！\***      |
| :----------------------------------------------------------- | :----------------------------------------------------------- |
| 1.[Java算法面试题（史上最强、持续更新、吐血推荐）](https://www.cnblogs.com/crazymakercircle/p/14377410.html) | 2.[Java基础面试题（史上最全、持续更新、吐血推荐）](https://www.cnblogs.com/crazymakercircle/p/14366081.html) |
| 3.[**JVM面试题**（史上最强、持续更新、吐血推荐）](https://www.cnblogs.com/crazymakercircle/p/14365820.html) | 4、[**架构设计面试题** （史上最全、持续更新、吐血推荐）](https://www.cnblogs.com/crazymakercircle/p/14367907.html) |
| 5、[Spring面试题 专题](https://www.cnblogs.com/crazymakercircle/p/14465630.html) | 6、[SpringMVC面试题 专题](https://www.cnblogs.com/crazymakercircle/p/14465630.html) |
| 7.[SpringBoot - 面试题（史上最强、持续更新）](https://www.cnblogs.com/crazymakercircle/p/14365487.html) | 8、[Tomcat面试题 专题部分](https://www.cnblogs.com/crazymakercircle/p/14465630.html) |
| 9.[**网络协议面试题**（史上最全、持续更新、吐血推荐）](https://www.cnblogs.com/crazymakercircle/p/14370335.html) | 10、[**TCP/IP协议**（图解+秒懂+史上最全）](https://www.cnblogs.com/crazymakercircle/p/14499211.html) |
| 11.[**JUC并发包与容器** - 面试题（史上最强、持续更新）](https://www.cnblogs.com/crazymakercircle/p/13904029.html) | 12、[**设计模式面试题** （史上最全、持续更新、吐血推荐）](https://www.cnblogs.com/crazymakercircle/p/14367101.html) |
| 13.[死锁面试题（史上最强、持续更新）](https://www.cnblogs.com/crazymakercircle/p/14323919.html) | 15.[**Zookeeper 分布式锁** （图解+秒懂+史上最全）](https://www.cnblogs.com/crazymakercircle/p/14504520.html) |
| 14、[Redis 面试题 - 收藏版（史上最强、持续更新）](https://www.cnblogs.com/crazymakercircle/p/13900198.html) | 16、[Zookeeper 面试题（史上最强、持续更新）](https://www.cnblogs.com/crazymakercircle/p/13900183.html) |
| 17、[分布式事务面试题 （史上最全、持续更新、吐血推荐）](https://www.cnblogs.com/crazymakercircle/p/14375424.html) | 18、[**一致性协议** （史上最全）](https://www.cnblogs.com/crazymakercircle/p/14334422.html) |
| 19、[Zab协议 (史上最全)](https://www.cnblogs.com/crazymakercircle/p/14339702.html) | 20、[Paxos 图解 (秒懂)](https://www.cnblogs.com/crazymakercircle/p/14341015.html) |
| 21、[raft 图解 (秒懂)](https://www.cnblogs.com/crazymakercircle/p/14343154.html) | 26、[消息队列、RabbitMQ、Kafka、RocketMQ面试题 （史上最全、持续更新）](https://www.cnblogs.com/crazymakercircle/p/14367425.html) |
| 22.[Linux面试题（史上最全、持续更新、吐血推荐）](https://www.cnblogs.com/crazymakercircle/p/14366893.html) | 23、[Mysql 面试题（史上最强、持续更新）](https://www.cnblogs.com/crazymakercircle/p/13900186.html) |
| 24、[SpringCloud 面试题 - 收藏版（史上最强、持续更新）](https://www.cnblogs.com/crazymakercircle/p/13900212.html) | 25、[Netty 面试题 （史上最强、持续更新）](https://www.cnblogs.com/crazymakercircle/p/13903625.html) |
| 27、[**内存泄漏 内存溢出**（史上最全）](https://www.cnblogs.com/crazymakercircle/p/14389201.html) | 28、[JVM 内存溢出 实战 （史上最全）](https://www.cnblogs.com/crazymakercircle/p/14389389.html) |
| 29、[**多线程面试题**（史上最全）](https://www.cnblogs.com/crazymakercircle/p/14655412.html) | 30、[千万小心HR面，过五关斩六将后，阴沟里可能翻船！](https://www.cnblogs.com/crazymakercircle/p/14659738.html) |
| [社群面试真题系列之1： 完美世界校招面试题](https://www.cnblogs.com/crazymakercircle/p/15474042.html) | [社群面试真题系列之2：字节 网易 菜鸟 有赞 哈啰 面试题](https://www.cnblogs.com/crazymakercircle/p/15474059.html) |



------

## 经典博文：入大厂 、做架构、大力提升Java 内功

| 入大厂 、做架构、大力提升Java 内功 必备的精彩博文            | 2021 秋招涨薪1W + 必备的精彩博文                             |
| :----------------------------------------------------------- | :----------------------------------------------------------- |
| 1：[**Redis 分布式锁** （图解-秒懂-史上最全）](https://www.cnblogs.com/crazymakercircle/p/14731826.html) | 2：[**Zookeeper 分布式锁** （图解-秒懂-史上最全）](https://www.cnblogs.com/crazymakercircle/p/14504520.html) |
| 9：[Redis集群 - 图解 - 秒懂（史上最全)](https://www.cnblogs.com/crazymakercircle/p/14698576.html) *10W长文* |                                                              |
| 3: [**Redis与MySQL双写一致性如何保证？**](https://www.cnblogs.com/crazymakercircle/p/14853622.html) （面试必备） | 4: [**面试必备：秒杀超卖 解决方案**](https://www.cnblogs.com/crazymakercircle/p/14846136.html) （史上最全） |
| 5:[**面试必备之：Reactor模式**](https://www.cnblogs.com/crazymakercircle/p/9833847.html) | 6: [**10分钟看懂， Java NIO 底层原理**](https://www.cnblogs.com/crazymakercircle/p/10225159.html) |
| 7:[**TCP/IP（图解+秒懂+史上最全）**](https://www.cnblogs.com/crazymakercircle/p/14499211.html) | 8：[Feign原理 （图解）](https://www.cnblogs.com/crazymakercircle/p/11965726.html) |
| 9:[DNS图解（秒懂 + 史上最全 + 高薪必备）](https://www.cnblogs.com/crazymakercircle/p/14976612.html) | 10：[CDN图解（秒懂 + 史上最全 + 高薪必备）](https://www.cnblogs.com/crazymakercircle/p/14978513.html) |
| 11: [**分布式事务（ 图解 + 史上最全 + 吐血推荐 ）**](https://www.cnblogs.com/crazymakercircle/p/13917517.html) | [12 限流（史上最全）：计数器、漏桶、令牌桶 三大算法的原理与框架](https://www.cnblogs.com/crazymakercircle/p/15187184.html) |



------

##  经典博文：Springcloud 从入门到精通

| [SpringCloud 入门教程（极速- 入门）](https://www.cnblogs.com/crazymakercircle/p/14017169.html) |                                                              |
| :----------------------------------------------------------- | :----------------------------------------------------------- |
| [SpringBootAdmin + SpringCloud 整合](https://www.cnblogs.com/crazymakercircle/p/14264200.html) |                                                              |
| 分库分表 -Sharding-JDBC- 从入门到精通 1 [Sharding-JDBC 分库、分表（入门实战)](https://www.cnblogs.com/crazymakercircle/p/14199586.html) | 分库分表 -Sharding-JDBC- 从入门到精通 2 [Sharding-JDBC 基础知识](https://www.cnblogs.com/crazymakercircle/p/14199620.html) |
| 分库分表 Sharding-JDBC 从入门到精通之 3 [**自定义主键、分布式雪花主键，原理与实战**](https://www.cnblogs.com/crazymakercircle/p/14221014.html) | 分库分表 -Sharding-JDBC- 从入门到精通 4 [**MYSQL集群主从复制，原理与实战**](https://www.cnblogs.com/crazymakercircle/p/14208947.html) |
| 分库分表 Sharding-JDBC 从入门到精通之 5 [**读写分离 实战**](https://www.cnblogs.com/crazymakercircle/p/14221076.html) | 分库分表 Sharding-JDBC 从入门到精通之 6 [**Sharding-JDBC执行原理**](https://www.cnblogs.com/crazymakercircle/p/14220982.html) |

| [**nacos 实战**（史上最全）](https://www.cnblogs.com/crazymakercircle/p/14231815.html) | [sentinel （史上最全+入门教程）](https://www.cnblogs.com/crazymakercircle/p/14285001.html) |
| :----------------------------------------------------------- | :----------------------------------------------------------- |
| [**SpringCloud gateway** （史上最全）](https://www.cnblogs.com/crazymakercircle/p/11704077.html) | [Webflux（史上最全）](https://www.cnblogs.com/crazymakercircle/p/14302151.html) |
| **[springcloud + webflux 高并发实战](https://www.cnblogs.com/crazymakercircle/p/14312282.html)** |                                                              |

------



## 书《[SpringCloud、Nginx高并发核心编程》【2020年11月 】](https://www.cnblogs.com/crazymakercircle/p/13878143.html)随书源码、阅读说明

初衷：为大家奉上一本Spring Cloud、Nginx“原理级”“思想级”经典图书，为帮助大家顺利进入互联网大厂、成为Java核心架构师尽一份绵薄之力

**1.1 相关的源码、勘误、声明、视频**

**机械工业出版社 《SpringCloud、Nginx高并发核心编程》相关的源码**

**随书源码 SpringCloud 部分** ： https://gitee.com/crazymaker/crazy-springcloud.git

**随书源码 nginx/openresty 部分** ： https://gitee.com/crazymaker/LuaDemoProject.git

**1.2 阅读之前，建议拥有的前置知识**

一、[SpringBoot 基础知识 核心知识 ](https://www.cnblogs.com/crazymakercircle/p/13895735.html) ，如果不了解springboot，则在阅读 《SpringCloud、Nginx高并发核心编程 》【2020年11月新书】前，此文必看

二、[SpringCloud 入门 案例](https://www.cnblogs.com/crazymakercircle/p/14017169.html)，帮您1分钟完成 SpringCloud 的开发入门 ，在阅读 《SpringCloud、Nginx高并发核心编程 》【2020年11月新书】前，此文必看

三、[Springcloud 中 SpringBoot 配置全集](https://www.cnblogs.com/crazymakercircle/p/11706764.html)

**以及有关Springcloud 几篇核心、重要的文章：**

一、[Springcloud 配置， 史上最全 一文全懂](https://www.cnblogs.com/crazymakercircle/p/11674597.html)

二、[Springcloud 中 SpringBoot 配置全集 ， 收藏版](https://www.cnblogs.com/crazymakercircle/p/11706764.html)

三、[Feign Ribbon Hystrix 三者关系 ， 史上最全 深度解析](https://www.cnblogs.com/crazymakercircle/p/11664812.html)

四、[SpringCloud gateway 详解 ， 史上最全](https://www.cnblogs.com/crazymakercircle/p/11704077.html)

五、[图解：tomcat的maxConnections、maxThreads、acceptCount | 秒懂](https://www.cnblogs.com/crazymakercircle/p/11748214.html)



**1.3 Crazy-SpringCloud 脚手架 系列图文&视频，陆续上线**

**基础入门：**      1、[Eureka 入门，带视频](https://www.cnblogs.com/crazymakercircle/p/12043538.html)     2、 [springcloud Config 入门，带视频 ](https://www.cnblogs.com/crazymakercircle/p/12043604.html)

​              3、[Zuul 网关入门，带视频](https://www.cnblogs.com/crazymakercircle/p/12046484.html)    4、[Zuul 网关修改请求头](https://www.cnblogs.com/crazymakercircle/p/12037587.html)  5、[Zuul 整合 Swagger 方便开发和调试](https://www.cnblogs.com/crazymakercircle/p/12046131.html)

**源码工程介绍**： [SpringCloud 脚手架](https://www.cnblogs.com/crazymakercircle/p/12041568.html)

**微服务安全：**    1、[spring security 原理+实战](https://www.cnblogs.com/crazymakercircle/p/12040402.html)   2、[SpingSecurity + SpringSession 死磕](https://www.cnblogs.com/crazymakercircle/p/12037584.html)

**分布式session：** 1、[SpringSession 独立使用 ](https://www.cnblogs.com/crazymakercircle/p/12038664.html)   2、[从0开始打造自己的 RedisSession ](https://www.cnblogs.com/crazymakercircle/p/12038208.html)



## 老书《[Netty  Redis Zookeeper 高并发实战](https://www.cnblogs.com/crazymakercircle/p/11397271.html)》随书源码、说明（@Deprecated）

***\*相关的源码：\****

**随书源码 1-8 章 10-11章** ： https://gitee.com/crazymaker/netty_redis_zookeeper_source_code.git

**随书源码 第9章  单体IM** ： https://gitee.com/crazymaker/SimpleCrayIM.git

**随书源码 第12章  crazyim** ： https://gitee.com/crazymaker/crazy_tourist_circle__im.git  项目实战指导：crayim[实战，Netty集群](https://www.cnblogs.com/crazymakercircle/p/11470287.html)实战

## 新书《Java高并发核心编程 卷1》【2021年4月新书 】随书源码、阅读说明

***\*相关的源码：\****

**随书源码 1-7 章 9-14章** ： https://gitee.com/crazymaker/netty_redis_zookeeper_source_code.git

**随书源码 第8章  单体IM** ： https://gitee.com/crazymaker/SimpleCrayIM.git

**随书源码 第15章  crazyim** ： https://gitee.com/crazymaker/crazy_tourist_circle__im.git  项目实战指导：crayim[实战，Netty集群](https://www.cnblogs.com/crazymakercircle/p/11470287.html)实战



------

## 新书《[J](https://www.cnblogs.com/crazymakercircle/p/11397271.html)ava高并发核心编程 卷2》【2021年4月新书 】随书源码、阅读说明

相关的源码：https://gitee.com/crazymaker/Java-high-concurrency-core-Programming-Volume-2-source-code

# **其他的高并发 分布式 核心知识**

**1 JUC并发包**

【1】 [CyclicBarrier 使用&核心原理 图解](https://www.cnblogs.com/crazymakercircle/p/13906379.html)    【2】 [countDownLatch 使用&核心原理 图解](https://www.cnblogs.com/crazymakercircle/p/13906922.html)

【3】[Semaphore 使用&核心原理 图解](https://www.cnblogs.com/crazymakercircle/p/13907012.html)       【4】 [跳表 核心原理 - （秒懂）](https://www.cnblogs.com/crazymakercircle/p/13925511.html)

【5】 [ConcurrentSkipListMap - 秒懂 ](https://www.cnblogs.com/crazymakercircle/p/13928386.html)        【6】 [ConcurrentSkipListSet - 秒懂 ](https://www.cnblogs.com/crazymakercircle/p/13928504.html)

**2 无锁编程 高并发框架**

【1】 [伪共享  核心原理](https://www.cnblogs.com/crazymakercircle/p/13909102.html)  【2】 [disruptor 框架的使用和原理 ](https://www.cnblogs.com/crazymakercircle/p/13909235.html) 【3】[Akka 框架的原理和入门实例](https://www.cnblogs.com/crazymakercircle/p/13910553.html)



3 分布式事务

【1】 [分布式事务  一文秒懂](https://www.cnblogs.com/crazymakercircle/p/13917517.html)



------

##  一键打造：地表最强 开发环境 系列

| 工欲善其事 必先利其器                                        |
| :----------------------------------------------------------- |
| 地表最强 开发环境： [vagrant+java+springcloud+redis+zookeeper镜像下载(&制作详解)](https://www.cnblogs.com/crazymakercircle/p/14194688.html) |
| 地表最强 热部署：[java SpringBoot SpringCloud 热部署 热加载 热调试](https://www.cnblogs.com/crazymakercircle/p/12077373.html) |
| 地表最强 发请求工具（再见吧， PostMan ）：[IDEA HTTP Client（史上最全）](https://www.cnblogs.com/crazymakercircle/p/14317222.html) |
| 地表最强 PPT 小工具： [屌炸天，像写代码一样写PPT](https://www.cnblogs.com/crazymakercircle/p/14326975.html) |
| [无编程不创客，无编程不创客，一大波编程高手正在疯狂创客圈交流、学习中! 找组织，GO](https://www.cnblogs.com/crazymakercircle/p/9904544.html) |



| Java 分布式 高并发 开发环境搭建         | 链接地址                                                     |
| :-------------------------------------- | :----------------------------------------------------------- |
| windows centos 虚拟机 安装&排坑         | [vagrant+java+springcloud+redis+zookeeper镜像下载(&制作详解)）](https://www.cnblogs.com/crazymakercircle/p/14194688.htmll) |
| centos mysql 安装&排坑                  | [centos mysql 笔记（内含vagrant mysql 镜像）](https://www.cnblogs.com/crazymakercircle/p/14196707.html) |
| linux kafka安装&排坑                    | [kafka springboot (或 springcloud ) 整合](https://www.cnblogs.com/crazymakercircle/p/14050681.html) |
| Linux openresty 安装                    | [Linux openresty 安装](https://www.cnblogs.com/crazymakercircle/p/12115651.html) |
| 【必须】Linux Redis 安装（带视频）      | [Linux Redis 安装（带视频）](https://www.cnblogs.com/crazymakercircle/p/11985983.html) |
|                                         | [redis集群架构知识（史上最强，面试必看）](https://www.cnblogs.com/crazymakercircle/p/14282108.html) |
| 【必须】Linux Zookeeper 安装（带视频）  | [Linux Zookeeper 安装, 带视频](https://www.cnblogs.com/crazymakercircle/p/12006500.html) |
|                                         | [ZooKeeper 客户端: GUI+命令行（史上最全）](https://www.cnblogs.com/crazymakercircle/p/14613474.html) |
| Windows Redis 安装（带视频）            | [Windows Redis 安装（带视频）](https://www.cnblogs.com/crazymakercircle/p/11973314.html) |
| RabbitMQ 离线安装（带视频）             | [RabbitMQ 离线安装（带视频）](https://www.cnblogs.com/crazymakercircle/p/11992763.html) |
| RocketMQ 原理 - 部署 - 入门             | [RocketMQ 原理 - 部署 - 入门 （图解）](https://www.cnblogs.com/crazymakercircle/p/14012420.html) |
| ElasticSearch 安装, 带视频              | [ElasticSearch 安装, 带视频](https://www.cnblogs.com/crazymakercircle/p/12001292.html) |
| Nacos 安装（带视频）                    | [Nacos 安装（带视频）](https://www.cnblogs.com/crazymakercircle/p/14194688.html) |
| 【必须】Eureka                          | [Eureka 入门，带视频](https://www.cnblogs.com/crazymakercircle/p/12043538.html) |
| 【必须】springcloud Config 入门，带视频 | [springcloud Config 入门，带视频](https://www.cnblogs.com/crazymakercircle/p/12043604.html) |
| 【必须】SpringCloud 脚手架打包与启动    | [SpringCloud脚手架打包与启动](https://www.cnblogs.com/crazymakercircle/p/12041568.html) |
| Linux 自启动 假死自启动 定时自启        | [Linux 自启动 假死启动](https://www.cnblogs.com/crazymakercircle/p/14022322.html) |

