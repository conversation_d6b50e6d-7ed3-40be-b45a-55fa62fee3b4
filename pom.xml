<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.crazymakercircle</groupId>
    <artifactId>Java-high-concurrency-core-Programming</artifactId>
    <version>1.0-SNAPSHOT</version>
    <modules>
        <module>CoccurrentDemos</module>
        <module>chatCommon</module>
        <module>thread-demos</module>
        <module>lock-demos</module>
    </modules>
    <packaging>pom</packaging>
    <!-- 依赖版本集中管理 -->
    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <java.version>1.8</java.version>
        <!--<springboot.version>2.0.0.RELEASE</springboot.version>-->
        <!--        <springboot.version>1.4.3.RELEASE</springboot.version>-->
        <springboot.version>1.5.9.RELEASE</springboot.version>
        <springframework.version>4.3.13.RELEASE</springframework.version>
        <springboot.redis.version>1.5.2.RELEASE</springboot.redis.version>
        <netty.version>4.1.49.Final</netty.version>
        <protobuf.version>3.6.1</protobuf.version>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <redis.version>2.6.2</redis.version>
        <!--<redis.version>2.9.0</redis.version>-->

        <curator.version>2.10.0</curator.version>
        <guava.version>17.0</guava.version>
        <httpclientutil.version>1.0</httpclientutil.version>
        <feign.form.spring.version>3.0.3</feign.form.spring.version>
        <spring.version>4.3.11.RELEASE</spring.version>
        <fastjson.version>1.2.83</fastjson.version>
        <rocketmq.version>4.1.0-incubating</rocketmq.version>
        <lombok.version>1.16.22</lombok.version>
        <httpclient.version>4.5.1</httpclient.version>
        <commons-io.version>2.6</commons-io.version>
    </properties>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.lmax</groupId>
                <artifactId>disruptor</artifactId>
                <version>3.4.2</version>
            </dependency>
            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>4.11</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>4.1</version>
            </dependency>
            <dependency>
                <groupId>org.openjdk.jol</groupId>
                <artifactId>jol-core</artifactId>
                <version>0.10</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <dependencies>

        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>

        </dependency>
        <!--

                <dependency>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter</artifactId>
                    <version>${springboot.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.projectlombok</groupId>
                    <artifactId>lombok</artifactId>
                    <version>${lombok.version}</version>
                    <scope>provided</scope>
                </dependency>

                <dependency>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-test</artifactId>
                    <version>${springboot.version}</version>
                    &lt;!&ndash;<scope>test</scope>&ndash;&gt;
                </dependency>
                <dependency>
                    <groupId>org.springframework.data</groupId>
                    <artifactId>spring-data-redis</artifactId>
                    <version>${springboot.redis.version}</version>

                </dependency>

                <dependency>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-devtools</artifactId>
                    <version>${springboot.version}</version>
                    <optional>true</optional>
                </dependency>
        -->


        <dependency>
            <groupId>org.apache.zookeeper</groupId>
            <artifactId>zookeeper</artifactId>
            <version>3.4.8</version>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.apache.curator</groupId>
            <artifactId>curator-client</artifactId>
            <version>4.0.0</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.zookeeper</groupId>
                    <artifactId>zookeeper</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.apache.curator</groupId>
            <artifactId>curator-framework</artifactId>
            <version>4.0.0</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.zookeeper</groupId>
                    <artifactId>zookeeper</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.curator</groupId>
            <artifactId>curator-recipes</artifactId>
            <version>4.0.0</version>

        </dependency>

        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
            <version>${redis.version}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-pool2</artifactId>
            <version>2.6.0</version>
        </dependency>


    </dependencies>


</project>