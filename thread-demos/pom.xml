<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <artifactId>thread-demos</artifactId>
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.crazymakercircle</groupId>
    <version>1.0-SNAPSHOT</version>


    <parent>
        <artifactId>Java-high-concurrency-core-Programming</artifactId>
        <groupId>com.crazymakercircle</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>

    <dependencies>
        <dependency>
            <groupId>org.openjdk.jol</groupId>
            <artifactId>jol-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.crazymakercircle</groupId>
            <artifactId>chat<PERSON><PERSON>mon</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
        </dependency>


    </dependencies>

</project>