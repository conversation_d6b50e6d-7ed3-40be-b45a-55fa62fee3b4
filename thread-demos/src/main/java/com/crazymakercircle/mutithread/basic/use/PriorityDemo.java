package com.crazymakercircle.mutithread.basic.use;

import com.crazymakercircle.util.Print;

/**
 * Created by 尼恩@疯狂创客圈.
 */

public class PriorityDemo {
    public static final int SLEEP_GAP = 1000;

    static class PrioritySetThread extends Thread {
        static int threadNo = 1;

        public PrioritySetThread() {
            super("thread-" + threadNo);
            threadNo++;
        }

        public long opportunities = 0;

        public void run() {
            for (int i = 0; ; i++) {
                opportunities++;
            }
        }
    }

    public static void main(String args[]) throws InterruptedException {

        PrioritySetThread[] threads = new PrioritySetThread[10];
        for (int i = 0; i < threads.length; i++) {
            threads[i] = new PrioritySetThread();
            //优先级的设置，从1-10
            threads[i].setPriority(i + 1);
        }

        for (PrioritySetThread thread : threads) {
            thread.start();

        }

        Thread.sleep(SLEEP_GAP);

        for (PrioritySetThread thread : threads) {
            thread.stop();
        }

        for (PrioritySetThread thread : threads) {
            Print.cfo(thread.getName() +
                    ";优先级为-" + thread.getPriority() +
                    ";机会值为-" + thread.opportunities
            );
        }

    }
}