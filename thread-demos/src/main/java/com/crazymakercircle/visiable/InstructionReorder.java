package com.crazymakercircle.visiable;

public class InstructionReorder {
    private /*volatile*/ static int x = 0, y = 0;
    private /*volatile*/ static int a = 0, b = 0;

    public static void main(String[] args) throws InterruptedException {
        int i = 0;
        for (; ; ) {
            i++;
            x = 0;
            y = 0;
            a = 0;
            b = 0;
            Thread one = new Thread(() -> {
                a = 1;   //①
                x = b;    //②
            });

            Thread other = new Thread(() -> {
                b = 1;  //③
                y = a;  //④
            });
            one.start();
            other.start();
            one.join();
            other.join();
            String result = "第" + i + "次 (" + x + "," + y + "）";
            if (x != 0 || y != 1) {
                System.err.println(result);
            }
        }
    }
}